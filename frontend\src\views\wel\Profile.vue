<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">

        <!-- 用户信息卡片 -->
        <div class="profile-card">
          <div class="avatar-section">
            <div class="avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="user-info">
              <h2>{{ displayUserInfo.name }}</h2>
              <p>{{ displayUserInfo.position }}</p>
              <p>{{ displayUserInfo.company }}</p>
            </div>
          </div>
          <div class="status-badge">
            <i class="fas fa-check-circle"></i>
            已报名
          </div>
        </div>

        <!-- 功能菜单 -->
        <div class="menu-container">

          <div class="menu-item" @click="handleMenuClick('mySchedule')">
            <div class="menu-icon">
              <i class="fas fa-calendar-check"></i>
            </div>
            <div class="menu-text">
              <span>我的日程</span>
              <small>个人参会日程安排</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="menu-item" @click="handleMenuClick('myDining')">
            <div class="menu-icon">
              <i class="fas fa-utensils"></i>
            </div>
            <div class="menu-text">
              <span>我的用餐</span>
              <small>查看用餐安排和餐券信息</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>

          <div class="menu-item" @click="handleMenuClick('myHotel')">
            <div class="menu-icon">
              <i class="fas fa-bed"></i>
            </div>
            <div class="menu-text">
              <span>我的住宿</span>
              <small>查看住宿详情和房间信息</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>

        <!-- 设置和退出 -->
        <div class="settings-container">
          <!-- <div class="menu-item" @click="handleMenuClick('settings')">
            <div class="menu-icon">
              <i class="fas fa-cog"></i>
            </div>
            <div class="menu-text">
              <span>设置</span>
              <small>应用设置和偏好</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>

          <div class="menu-item logout-item" @click="handleLogout">
            <div class="menu-icon">
              <i class="fas fa-sign-out-alt"></i>
            </div>
            <div class="menu-text">
              <span>退出登录</span>
              <small>安全退出当前账户</small>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div> -->
        </div>
      </div>
    </main>

  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Profile',
  computed: {
    ...mapGetters(['userInfo']),
    displayUserInfo() {
      return {
        name: this.userInfo?.user_name || this.userInfo?.name || '未知用户',
        position: this.userInfo?.role_name || this.userInfo?.position || '未知职位',
        company: this.userInfo?.dept_name || this.userInfo?.company || '未知公司'
      }
    }
  },
  async created() {
    await this.loadUserInfo()
  },
  methods: {
    ...mapActions(['GetUserInfo']),
    async loadUserInfo() {
      try {
        // 如果 store 中没有用户信息，尝试获取
        if (!this.userInfo || Object.keys(this.userInfo).length === 0) {
          await this.GetUserInfo()
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    /**
     * 处理菜单点击
     */
    handleMenuClick(menuType) {
      switch (menuType) {
        case 'myInfo':
          this.showMyInfo();
          break;
        case 'myTickets':
          this.showMyTickets();
          break;
        case 'mySchedule':
          this.showMySchedule();
          break;
        case 'myPhotos':
          this.showMyPhotos();
          break;
        case 'myDining':
          this.showMyDining();
          break;
        case 'myHotel':
          this.showMyHotel();
          break;
        case 'settings':
          this.showSettings();
          break;
        default:
          console.log('未知菜单类型:', menuType);
      }
    },

    /**
     * 我的信息
     */
    showMyInfo() {
      if (this.$message) {
        this.$message.info('我的信息功能');
      } else {
        alert('我的信息功能\n（演示功能）');
      }
    },

    /**
     * 我的门票
     */
    showMyTickets() {
      if (this.$message) {
        this.$message.info('我的门票功能');
      } else {
        alert('我的门票功能\n（演示功能）');
      }
    },

    /**
     * 我的日程
     */
    showMySchedule() {
      this.$emit('navigate', 'my-schedule');
    },

    /**
     * 我的相册
     */
    showMyPhotos() {
      if (this.$message) {
        this.$message.info('我的相册功能');
      } else {
        alert('我的相册功能\n（演示功能）');
      }
    },

    /**
     * 我的用餐
     */
    showMyDining() {
      this.$emit('navigate', 'my-dining');
    },

    /**
     * 我的住宿
     */
    showMyHotel() {
      this.$emit('navigate', 'my-accommodation');
    },

    /**
     * 设置
     */
    showSettings() {
      if (this.$message) {
        this.$message.info('设置功能');
      } else {
        alert('设置功能\n（演示功能）');
      }
    },

    /**
     * 退出登录
     */
    handleLogout() {
      const confirmLogout = () => {
        if (this.$message) {
          this.$message.success('已退出登录');
        } else {
          alert('已退出登录\n（演示功能）');
        }
      };

      if (this.$confirm) {
        this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          confirmLogout();
        }).catch(() => {
          // 用户取消
        });
      } else {
        if (confirm('确定要退出登录吗？')) {
          confirmLogout();
        }
      }
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
}

.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
    margin-bottom: 15px;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

/* 用户信息卡片样式 */
.profile-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #4682B4;
    position: relative;
}

.avatar-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.avatar i {
    font-size: 28px;
    color: white;
}

.user-info h2 {
    color: #333;
    font-size: 20px;
    margin-bottom: 5px;
    font-weight: 600;
}

.user-info p {
    color: #666;
    font-size: 13px;
    margin: 2px 0;
    line-height: 1.4;
}

.status-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.status-badge i {
    font-size: 12px;
}

/* 功能菜单样式 */
.menu-container {
    margin-bottom: 25px;
}

.menu-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 3px solid #4682B4;
}

.menu-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    background: rgba(70, 130, 180, 0.05);
}

.menu-icon {
    width: 45px;
    height: 45px;
    background: rgba(70, 130, 180, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.menu-icon i {
    font-size: 20px;
    color: #4682B4;
}

.menu-text {
    flex: 1;
}

.menu-text span {
    color: #333;
    font-size: 16px;
    font-weight: 500;
    display: block;
    margin-bottom: 3px;
}

.menu-text small {
    color: #666;
    font-size: 12px;
    line-height: 1.3;
}

.menu-item > i {
    color: #999;
    font-size: 14px;
    margin-left: 10px;
}

/* 设置容器样式 */
.settings-container {
    border-top: 1px solid rgba(70, 130, 180, 0.1);
    padding-top: 20px;
}

.logout-item {
    border-left: 3px solid #dc3545;
}

.logout-item .menu-icon {
    background: rgba(220, 53, 69, 0.1);
}

.logout-item .menu-icon i {
    color: #dc3545;
}

.logout-item:hover {
    background: rgba(220, 53, 69, 0.05);
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 480px) {


    .list-container {
        padding: 15px;
    }

    .profile-card {
        padding: 15px;
    }

    .avatar {
        width: 50px;
        height: 50px;
    }

    .avatar i {
        font-size: 24px;
    }

    .user-info h2 {
        font-size: 18px;
    }

    .menu-item {
        padding: 12px;
    }

    .menu-icon {
        width: 40px;
        height: 40px;
    }

    .menu-icon i {
        font-size: 18px;
    }

    .menu-text span {
        font-size: 14px;
    }

    .menu-text small {
        font-size: 11px;
    }
}
</style>