import dd from 'dingtalk-jsapi';

const DING_AGENT_ID = import.meta.env.VITE_DING_AGENT_ID;
const DING_CORP_ID = import.meta.env.VITE_DING_CORP_ID;

const JS_APIS_MIN = ['biz.util.scan'];

let initialized = false;
let readyPromise = null;
let lastSignedUrl = '';

function assertEnvVars() {
  if (!DING_AGENT_ID || !DING_CORP_ID) {
    throw new Error('缺少环境变量：VITE_DING_AGENT_ID 或 VITE_DING_CORP_ID');
  }
}

function createNonceStr() {
  return Math.random().toString(36).slice(2, 17);
}

function createTimestamp() {
  return '' + Math.round(Date.now() / 1000);
}

function isInDingTalk() {
  const ua = navigator.userAgent || '';
  return typeof dd !== 'undefined' && /AliApp\(DingTalk/.test(ua);
}

async function fetchSignature(url, nonceStr, timeStamp) {
  const cleanUrl = url.split('#')[0];
  const qs = new URLSearchParams({
    url: cleanUrl,
    nonceStr,
    timeStamp,
  });
  const resp = await fetch(`/dingapp/user/getDingTalkApiParam?${qs.toString()}`, {
    method: 'GET',
    credentials: 'include',
  });
  if (!resp.ok) {
    throw new Error(`签名接口请求失败: HTTP ${resp.status}`);
  }
  const data = await resp.json().catch(() => ({}));
  const signature = data && data.signature;
  if (!signature) {
    throw new Error(`签名接口返回无效：${JSON.stringify(data)}`);
  }
  return signature;
}

function doDdConfig(params) {
  dd.config({
    agentId: params.agentId,
    corpId: params.corpId,
    timeStamp: Number(params.timeStamp),
    nonceStr: params.nonceStr,
    signature: params.signature,
    type: 0,
    jsApiList: params.jsApiList,
  });
}

/**
 * 初始化钉钉JSAPI（最小权限：扫码）
 * - 前端生成 nonceStr/timeStamp
 * - 调用 /dingapp/user/getDingTalkApiParam 获取 signature
 * - 调用 dd.config 完成鉴权
 */
export async function initDingTalk(url = window.location.href, jsApiList = [...JS_APIS_MIN]) {
  const cleanUrl = url.split('#')[0];
  if (initialized && lastSignedUrl === cleanUrl) {
    return ready();
  }
  assertEnvVars();

  if (!isInDingTalk()) {
    console.warn('[DingTalk] 当前不在钉钉内打开，将跳过 dd.config。');
    initialized = true;
    lastSignedUrl = cleanUrl;
    readyPromise = Promise.resolve();
    return readyPromise;
  }

  const nonceStr = createNonceStr();
  const timeStamp = createTimestamp();

  readyPromise = new Promise(async (resolve, reject) => {
    try {
      const signature = await fetchSignature(url, nonceStr, timeStamp);
      doDdConfig({
        agentId: String(DING_AGENT_ID),
        corpId: String(DING_CORP_ID),
        timeStamp,
        nonceStr,
        signature,
        jsApiList,
      });

      dd.ready(() => {
        initialized = true;
        lastSignedUrl = cleanUrl;
        resolve();
      });

      dd.error((err) => {
        initialized = false;
        console.error('[DingTalk] dd.error:', err);
        readyPromise = null;
        reject(err);
      });
    } catch (e) {
      initialized = false;
      readyPromise = null;
      reject(e);
    }
  });

  return readyPromise;
}

export function ready() {
  if (readyPromise) return readyPromise;
  return Promise.reject(new Error('DingTalk 未初始化，请先调用 initDingTalk()'));
}

export async function reconfigForUrl(newUrl, jsApiList = [...JS_APIS_MIN]) {
  initialized = false;
  readyPromise = null;
  lastSignedUrl = '';
  return initDingTalk(newUrl, jsApiList);
}

export async function scan(options) {
  await initDingTalk(window.location.href, Array.from(new Set([...JS_APIS_MIN])));
  if (!isInDingTalk()) {
    throw new Error('当前不在钉钉内，无法使用扫码');
  }
  return new Promise((resolve, reject) => {
    dd.biz.util.scan({
      type: options?.type || 'qr',
      onSuccess: (res) => resolve(res),
      onFail: (err) => reject(err),
    });
  });
}

export async function setTitle(title) {
  await ready();
  if (!isInDingTalk()) return;
  try {
    dd.biz.navigation.setTitle({ title });
  } catch (e) {
    console.warn('[DingTalk] 设置标题失败:', e);
  }
}