package org.springblade.modules.dingding.controller;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.dingding.service.IDingAppService;
import org.springblade.modules.dingding.util.DdConfigSign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@Slf4j
@RequestMapping("/dingapp/user")
public class DingAppController {

	@Autowired
    private IDingAppService dingAppService;


    @GetMapping("getDingTalkApiParam")
    public Kv getDingTalkApiParam(@RequestParam(name = "url")String url,
                                      @RequestParam(name = "nonceStr")String nonceStr,
                                      @RequestParam(name = "timeStamp")Long timeStamp){
        Kv kv=Kv.create();
        try{
            String ticket=dingAppService.getJsapiTicket();
            String signature= DdConfigSign.sign(ticket,nonceStr,timeStamp,url);
            kv.put("code",200);
            kv.put("signature",signature);
        }catch (Exception e){
            kv.put("code",500);
            kv.put("signature",e.getMessage());
            log.error(e.getMessage());
        }
        return kv;
    }




}
